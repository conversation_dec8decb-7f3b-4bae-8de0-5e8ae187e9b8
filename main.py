import cv2
import numpy as np

def main():
    # Open default webcam
    cap = cv2.VideoCapture(0)
    if not cap.isOpened():
        print("Error: Unable to open webcam")
        return

    # Read the first frame
    ret, first_frame = cap.read()
    if not ret:
        print("Error: Unable to read first frame")
        cap.release()
        return

    # Select ROI (press SPACE/ENTER to confirm, c to cancel)
    x, y, w, h = cv2.selectROI(
        "Select Object (SPACE/ENTER to confirm, c to cancel)",
        first_frame,
        fromCenter=False,
        showCrosshair=True
    )
    cv2.destroyAllWindows()
    if w == 0 and h == 0:
        print("Selection cancelled.")
        cap.release()
        return

    # Store original bounding box size
    orig_w, orig_h = w, h

    # Extract ROI and convert to grayscale
    roi = first_frame[y:y+h, x:x+w]
    roi_gray = cv2.cvtColor(roi, cv2.COLOR_BGR2GRAY)

    # Detect good features to track within ROI
    p0 = cv2.goodFeaturesToTrack(
        roi_gray,
        maxCorners=200,
        qualityLevel=0.3,
        minDistance=7
    )
    # Offset feature coordinates to full frame
    p0 = p0 + np.array([[x, y]], dtype=np.float32)

    # Parameters for Lucas-Kanade optical flow
    lk_params = dict(
        winSize=(15, 15),
        maxLevel=2,
        criteria=(cv2.TERM_CRITERIA_EPS | cv2.TERM_CRITERIA_COUNT, 10, 0.03)
    )

    prev_gray = cv2.cvtColor(first_frame, cv2.COLOR_BGR2GRAY)

    while True:
        ret, frame = cap.read()
        if not ret:
            break
        frame_gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)

        # Calculate optical flow for feature points
        p1, st, err = cv2.calcOpticalFlowPyrLK(
            prev_gray, frame_gray, p0, None, **lk_params
        )

        # Keep only successfully tracked points
        if p1 is not None and st is not None:
            # Reshape to Nx2 for centroid computation
            pts = p1[st.flatten() == 1].reshape(-1, 2)

            if len(pts) > 0:
                # Compute centroid of tracked points
                centroid = np.mean(pts, axis=0)
                cx, cy = int(centroid[0]), int(centroid[1])

                # Compute fixed-size bounding box around centroid
                top_left = (cx - orig_w // 2, cy - orig_h // 2)
                bottom_right = (cx + orig_w // 2, cy + orig_h // 2)
                cv2.rectangle(
                    frame,
                    top_left,
                    bottom_right,
                    (0, 255, 0),
                    2
                )

                # Update points for next frame in proper shape
                p0 = pts.reshape(-1, 1, 2).astype(np.float32)

        # Display the result
        cv2.imshow("Lucas-Kanade Fixed-Size Tracking", frame)

        key = cv2.waitKey(30) & 0xFF
        if key == 27:  # ESC key to exit
            break

        prev_gray = frame_gray.copy()

    # Release resources and close windows
    cap.release()
    cv2.destroyAllWindows()

if __name__ == "__main__":
    main()
