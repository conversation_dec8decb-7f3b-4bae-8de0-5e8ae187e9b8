# Eyego Task - Real-Time Object Tracking

A computer vision application that implements real-time object tracking using the Lucas-Kanade optical flow algorithm. The application allows users to select an object from their webcam feed and tracks it in real-time with a fixed-size bounding box.

## Features

- **Interactive Object Selection**: Click and drag to select any object in the webcam feed
- **Real-Time Tracking**: Uses Lucas-Kanade optical flow for smooth object tracking
- **Fixed-Size Bounding Box**: Maintains consistent bounding box dimensions throughout tracking
- **Feature Point Detection**: Automatically detects and tracks good features within the selected region
- **Robust Tracking**: Handles partial occlusion and maintains tracking through feature point updates

## Requirements

### System Requirements
- Python 3.7 or higher
- Webcam or camera device
- Operating System: Windows, macOS, or Linux

### Python Dependencies
- OpenCV (`cv2`) - Computer vision library
- NumPy - Numerical computing library

## Installation

### 1. Clone or Download the Project
```bash
git clone <repository-url>
cd "Eyego Task"
```

### 2. Install Dependencies

#### Option A: Using pip
```bash
pip install opencv-python numpy
```

#### Option B: Using conda
```bash
conda install opencv numpy
```

#### Option C: Create a virtual environment (recommended)
```bash
# Create virtual environment
python -m venv venv

# Activate virtual environment
# On Windows:
venv\Scripts\activate
# On macOS/Linux:
source venv/bin/activate

# Install dependencies
pip install opencv-python numpy
```

## Usage

### Running the Application
```bash
python main.py
```

### How to Use
1. **Start the Application**: Run the command above
2. **Select Object**: 
   - A window will open showing your webcam feed
   - Click and drag to select the object you want to track
   - Press `SPACE` or `ENTER` to confirm selection
   - Press `c` to cancel selection
3. **Track Object**: 
   - The application will start tracking the selected object
   - A green bounding box will follow the object
   - Press `ESC` to exit the application

## Implementation Details

### Algorithm Overview
The application uses the **Lucas-Kanade Optical Flow** algorithm combined with **Good Features to Track** for robust object tracking.

### Key Components

#### 1. Feature Detection
- Uses `cv2.goodFeaturesToTrack()` to detect corner points within the selected ROI
- Parameters:
  - `maxCorners=200`: Maximum number of feature points
  - `qualityLevel=0.3`: Quality threshold for corner detection
  - `minDistance=7`: Minimum distance between detected corners

#### 2. Optical Flow Tracking
- Implements Lucas-Kanade pyramidal optical flow (`cv2.calcOpticalFlowPyrLK`)
- Parameters:
  - `winSize=(15, 15)`: Search window size
  - `maxLevel=2`: Pyramid levels for multi-scale tracking
  - `criteria`: Termination criteria for iterative algorithm

#### 3. Centroid-Based Bounding Box
- Calculates centroid of successfully tracked feature points
- Maintains fixed-size bounding box around the centroid
- Preserves original selection dimensions throughout tracking

### Technical Flow
1. **Initialization**: Capture first frame and allow user to select ROI
2. **Feature Extraction**: Detect good features within the selected region
3. **Tracking Loop**:
   - Calculate optical flow for feature points
   - Filter out unsuccessfully tracked points
   - Compute centroid of remaining points
   - Draw fixed-size bounding box around centroid
   - Update feature points for next iteration

### Advantages of This Approach
- **Robust to Partial Occlusion**: Multiple feature points provide redundancy
- **Computationally Efficient**: Lucas-Kanade is faster than template matching
- **Adaptive**: Feature points are updated each frame to handle appearance changes
- **Fixed-Size Output**: Consistent bounding box size regardless of object scale changes

### Limitations
- May lose tracking if object moves too quickly
- Performance depends on the number and quality of detectable features
- Fixed bounding box size doesn't adapt to object scale changes
- Requires good lighting conditions for optimal feature detection

## Troubleshooting

### Common Issues

#### "Error: Unable to open webcam"
- Ensure your webcam is connected and not being used by another application
- Try changing the camera index in `cv2.VideoCapture(0)` to `cv2.VideoCapture(1)` or higher

#### Poor Tracking Performance
- Select objects with rich texture and distinct features
- Ensure good lighting conditions
- Avoid selecting objects that are too small or too large

#### Application Crashes
- Verify all dependencies are installed correctly
- Check that your Python version is 3.7 or higher

## Development

### Project Structure
```
Eyego Task/
├── main.py          # Main application file
└── README.md        # This file
```

### Extending the Application
The code is modular and can be extended with:
- Multiple object tracking
- Object classification
- Recording and playback functionality
- Different tracking algorithms (CSRT, KCF, etc.)

## License

This project is provided as-is for educational and research purposes.

## Contributing

Feel free to submit issues and enhancement requests!
